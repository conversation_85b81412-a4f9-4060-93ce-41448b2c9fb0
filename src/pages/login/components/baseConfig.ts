import { getRequestBaseUrl } from '@/api/utils'
import { API_URL } from '@/env'
import { LoginType } from '@/integration/guc'
import { useLocalStorage } from '@vueuse/core'

export interface BaseConfigItem {
  /**
   * Api endpoint
   */
  host?: string

  /**
   * Base name
   */
  name: string

  isDev?: boolean
}

export const baseConfigs: BaseConfigItem[] = [
  {
    host: 'http://mobd-api.caas-cloud-test.geega.com/',
    name: '测试环境',
  },
  {
    host: getRequestBaseUrl(API_URL),
    name: '开发环境',
    isDev: true,
  },
]

export const loginStorage = useLocalStorage(`save-login-form-info`, {
  remembered: false,
  type: LoginType.Password,
  username: '',
  password: '',
})
