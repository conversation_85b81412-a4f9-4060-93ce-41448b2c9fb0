/**
 *
 * 状态 TO_BE:待训练、ING:训练中，TO_BE_TEST:待考试，FAILED_TEST:考试未通过，PASSED_TEST:考试通过
 */
export enum TrainingStatus {
  TO_BE = 'TO_BE',
  ING = 'ING',
  TO_BE_TEST = 'TO_BE_TEST',
  FAILED_TEST = 'FAILED_TEST',
  PASSED_TEST = 'PASSED_TEST',
}

export const TrainingStatusOptions = [
  {
    label: '待训练',
    value: TrainingStatus.TO_BE,
  },
  {
    label: '训练中',
    value: TrainingStatus.ING,
  },
  {
    label: '待考试',
    value: TrainingStatus.TO_BE_TEST,
  },
  {
    label: '考试未通过',
    value: TrainingStatus.FAILED_TEST,
  },
  {
    label: '考试通过',
    value: TrainingStatus.PASSED_TEST,
  },
]

/**
 * 项目类型(1:训练、2:考核、3:比赛)
 */
export enum TrainingProjectType {
  Training = '1',
  Exam = '2',
  Race = '3',
}
