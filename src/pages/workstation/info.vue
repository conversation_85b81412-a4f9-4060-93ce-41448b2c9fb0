<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 工位信息数据
const workstationInfo = ref<any>({})
const loading = ref(true)
const isSubscribed = ref(false)
const showVideoDialog = ref(false)
const videoPlayer = ref<HTMLVideoElement>()

// 从路由参数获取工位信息
onMounted(() => {
  try {
    // 从路由查询参数中获取工位信息
    const data = route.query.data as string
    if (data) {
      workstationInfo.value = JSON.parse(decodeURIComponent(data))
    }
  }
  catch (error) {
    console.error('解析工位信息失败:', error)
  }
  finally {
    loading.value = false
  }
})

// 显示工艺学习视频
function showLearningVideo() {
  if (!workstationInfo.value.videoUrl) {
    console.warn('未配置学习视频URL')
    return
  }

  showVideoDialog.value = true
  // 延迟一下确保弹窗已经显示，然后开始播放视频
  setTimeout(() => {
    if (videoPlayer.value) {
      videoPlayer.value.load() // 重新加载视频源
      videoPlayer.value.play()
    }
  }, 100)
}

// 关闭视频弹窗
function closeVideoDialog() {
  showVideoDialog.value = false
}

// 订阅/取消订阅项目
function toggleSubscription() {
  isSubscribed.value = !isSubscribed.value

  if (isSubscribed.value) {
    // 调用订阅API
    console.log('订阅项目:', workstationInfo.value.projectName)
    // TODO: 调用订阅接口
  }
  else {
    // 调用取消订阅API
    console.log('取消订阅项目:', workstationInfo.value.projectName)
    // TODO: 调用取消订阅接口
  }
}

// 返回上一页
function goBack() {
  router.go(-1)
}
</script>

<template>
  <div class="workstation-info-page">
    <VanNavBar
      title="工位信息"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <div v-if="loading" class="loading-container">
      <VanLoading type="spinner" />
      <div class="mt-2">
        加载中...
      </div>
    </div>

    <div v-else class="content p-4">
      <div class="info-card rounded-lg bg-white p-4 shadow-sm">
        <div class="header mb-4">
          <div class="title text-lg text-gray-8 font-bold">
            {{ workstationInfo.workstationName || '未知工位' }}
          </div>
          <div v-if="workstationInfo.levelName" class="subtitle mt-1 text-sm text-gray-5">
            所属层级：{{ workstationInfo.levelName }}
          </div>
        </div>

        <div class="info-list">
          <div v-if="workstationInfo.userName" class="info-item">
            <div class="label">
              当前用户
            </div>
            <div class="value">
              {{ workstationInfo.userName }}
            </div>
          </div>

          <div v-if="workstationInfo.projectName" class="info-item">
            <div class="label">
              当前项目
            </div>
            <div class="value">
              {{ workstationInfo.projectName }}
            </div>
          </div>

          <div v-if="workstationInfo.videoUrl" class="info-item">
            <div class="label">
              学习视频
            </div>
            <div class="value text-blue-6">
              已配置
            </div>
          </div>

          <div v-if="workstationInfo.timestamp" class="info-item">
            <div class="label">
              扫码时间
            </div>
            <div class="value">
              {{ new Date(workstationInfo.timestamp).toLocaleString() }}
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons mt-4">
        <div class="flex gap-3">
          <!-- 学习视频按钮 -->
          <VanButton
            v-if="workstationInfo.videoUrl"
            type="primary"
            size="large"
            class="flex-1"
            @click="showLearningVideo"
          >
            学习视频
          </VanButton>

          <!-- 订阅项目按钮 -->
          <VanButton
            :type="isSubscribed ? 'default' : 'primary'"
            size="large"
            :class="workstationInfo.videoUrl ? 'subscribe-btn flex-1' : 'subscribe-btn'"
            class="flex-1"
            @click="toggleSubscription"
          >
            {{ isSubscribed ? '已订阅该项目' : '订阅该项目' }}
          </VanButton>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions mt-4">
        <VanButton
          type="default"
          block
          class="mb-2"
          @click="goBack"
        >
          返回
        </VanButton>
      </div>
    </div>

    <!-- 工艺学习视频弹窗 -->
    <van-dialog
      v-model:show="showVideoDialog"
      :close-on-click-overlay="true"
      width="90%"
      @close="closeVideoDialog"
    >
      <div class="video-dialog-content p-2">
        <!-- 视频播放器 -->
        <div class="video-player">
          <video
            ref="videoPlayer"
            width="100%"
            height="340"
            controls
            preload="auto"
            class="rounded-lg"
          >
            <source :src="workstationInfo.videoUrl || '/example.mp4'" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style lang="less" scoped>
.workstation-info-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.info-card {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #666;
      font-size: 14px;
    }

    .value {
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.video-section {
  .section-header {
    background: linear-gradient(135deg, #4f8ef7 0%, #3b82f6 100%);
  }

  .video-content {
    border-top: none;
  }
}

.subscribe-section {
  .subscribe-btn {
    height: 48px;
    font-size: 16px;
    font-weight: bold;

    &.van-button--default {
      background-color: #f0f0f0;
      color: #666;
      border-color: #f0f0f0;
    }
  }
}

.video-dialog-content {
  .video-player {
    video {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
